import { t as translation } from 'i18next';
import { _ } from 'utils/lodash';

const definedKeys = (t) => ({
  parentAadhaar: t('concatLabel', { label: t('parent'), type: t('aadhaar') }),
  motherAadhaar: t('concatLabel', { label: t('mother'), type: t('aadhaar') }),
  fatherAadhaar: t('concatLabel', { label: t('father'), type: t('aadhaar') }),
  guardianAadhaar: t('concatLabel', { label: t('guardian'), type: t('aadhaar') }),
  studentAadhaar: t('concatLabel', { label: t('student'), type: t('aadhaar') })
});

export const translateKey = (key) => _.get(definedKeys(translation), key, translation(key));
