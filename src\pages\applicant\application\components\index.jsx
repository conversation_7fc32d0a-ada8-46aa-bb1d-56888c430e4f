import React, { useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { useTranslation } from 'react-i18next';
import { useParams } from 'react-router-dom';
import { Box } from 'common/components';
import { StepperStep } from 'common/custom-components';
import { actions as commonActions } from 'pages/common/slice';
import BankDetails from './BankDetails';
import AcademicDetails from './AcademicDetails';
import Documents from './Documents';
import ApplicantDetailsMain from './ApplicantDetailsMain';
import ReviewSubmit from './ReviewSubmit';
import ParentDetailsMain from './ParentDetailsMain';
import { STEPPER_STEPS } from '../constants';
import { actions as sliceActions } from '../slice';
import { getCurrentStep, getApplicationId } from '../selectors';
import { useGetApplicationDetailsQuery } from '../api';

const Applicant = () => {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const { applicationId: urlApplicationId } = useParams();
  const currentStep = useSelector(getCurrentStep);
  const storeApplicationId = useSelector(getApplicationId);

  const applicationId = urlApplicationId || storeApplicationId;
  const {
    data: { payload: applicationDetails = {} } = {},
    isSuccess: isDetailsSuccess
  } = useGetApplicationDetailsQuery(applicationId, {
    skip: !applicationId
  });

  useEffect(() => {
    if (urlApplicationId && !storeApplicationId) {
      dispatch(sliceActions.setApplicationId(urlApplicationId));
    } else if (!urlApplicationId && storeApplicationId) {
      dispatch(sliceActions.clearAll());
    }
  }, [urlApplicationId, storeApplicationId, dispatch]);

  useEffect(() => {
    if (isDetailsSuccess && applicationDetails?.personalDetails) {
      if (applicationDetails?.personalDetails?.educationQualification) {
        const { id, qualification } = applicationDetails.personalDetails?.educationQualification;
        dispatch(commonActions.setScholarshipTypeId(id));
        localStorage.setItem('scholarshipTypeId', id);

        const scholarshipTypeMapping = {
          HSS: 'higherSecondary',
          UG: 'underGraduate',
          PG: 'masterGraduation'
        };

        const scholarshipType = scholarshipTypeMapping[qualification];
        if (scholarshipType) {
          dispatch(commonActions.setScholarshipType(scholarshipType));
          localStorage.setItem('scholarshipType', scholarshipType);
        }
      }
      if (applicationDetails.parentGuardianDetails?.id) {
        dispatch(sliceActions.setFormId({
          formType: 'parentGuardianId',
          id: applicationDetails.parentGuardianDetails.id
        }));
      }
    }
  }, [isDetailsSuccess, applicationDetails, dispatch]);

  const steps = [
    t('personalDetails'),
    t('parentGuardianInfo'),
    t('bankDetails'),
    t('academicDetails'),
    t('documents'),
    t('reviewSubmit')
  ];

  const handleNext = () => {
    dispatch(sliceActions.nextStep());
  };

  const handlePrevious = () => {
    dispatch(sliceActions.previousStep());
  };

  const handleEdit = (section) => {
    const stepMap = {
      [STEPPER_STEPS.APPLICANT_DETAILS]: 0,
      [STEPPER_STEPS.PARENT_DETAILS]: 1,
      [STEPPER_STEPS.BANK_DETAILS]: 2,
      [STEPPER_STEPS.ACADEMIC_DETAILS]: 3,
      [STEPPER_STEPS.DOCUMENTS_UPLOAD]: 4,
      [STEPPER_STEPS.REVIEW_SUBMIT]: 5
    };
    dispatch(sliceActions.setCurrentStep(stepMap[section]));
  };

  const renderCurrentStep = () => {
    switch (currentStep) {
      case 0:
        return (
          <ApplicantDetailsMain
            onNext={handleNext}
            onPrevious={currentStep > 0 ? handlePrevious : null}
            applicationDetails={applicationDetails}
            isDetailsSuccess={isDetailsSuccess}
            applicationId={applicationId}
          />
        );
      case 1:
        return (
          <ParentDetailsMain
            onNext={handleNext}
            onPrevious={currentStep > 0 ? handlePrevious : null}
            applicationDetails={applicationDetails}
            isDetailsSuccess={isDetailsSuccess}
            applicationId={applicationId}
          />
        );
      case 2:
        return (
          <BankDetails
            onNext={handleNext}
            onPrevious={currentStep > 0 ? handlePrevious : null}
            applicationDetails={applicationDetails}
            isDetailsSuccess={isDetailsSuccess}
          />
        );
      case 3:
        return (
          <AcademicDetails
            onNext={handleNext}
            onPrevious={currentStep > 0 ? handlePrevious : null}
            applicationDetails={applicationDetails}
            isDetailsSuccess={isDetailsSuccess}
          />
        );
      case 4:
        return (
          <Documents
            onNext={handleNext}
            onPrevious={currentStep > 0 ? handlePrevious : null}
            applicationDetails={applicationDetails}
            isDetailsSuccess={isDetailsSuccess}
          />
        );
      case 5:
        return (
          <ReviewSubmit
            onPrevious={currentStep > 0 ? handlePrevious : null}
            onEdit={handleEdit}
            applicationDetails={applicationDetails}
            isDetailsSuccess={isDetailsSuccess}
            applicationId={applicationId}
          />
        );
      default:
        return null;
    }
  };

  useEffect(() => {
    const storedId = localStorage.getItem('scholarshipTypeId');
    const storedName = localStorage.getItem('scholarshipType');

    if (storedId) {
      dispatch(commonActions.setScholarshipTypeId(storedId));
    }
    if (storedName) {
      dispatch(commonActions.setScholarshipType(storedName));
    }
  }, [dispatch]);

  return (
    <Box py={{ base: 2, md: 4 }}>
      {/* Stepper */}
      <Box mb={{ base: 0, md: 8 }} display={{ base: 'none', md: 'block' }}>
        <StepperStep
          steps={steps}
          currentStep={currentStep}
          spacing={{ base: 2, md: 4 }}
        />
      </Box>

      {/* Current Step Content */}
      {renderCurrentStep()}
    </Box>
  );
};

export default Applicant;
