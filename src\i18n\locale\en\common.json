{"aadhaar": "<PERSON><PERSON><PERSON><PERSON>", "aadhaarCard": "<PERSON><PERSON><PERSON><PERSON>", "aadhaarDescription": "Government issued identity proof", "aadhaarId": "Aadhaar <PERSON>", "aadhaarInvalidNumber": "<PERSON><PERSON><PERSON>", "aadhaarLinkedMobileNo": "<PERSON><PERSON><PERSON><PERSON> Linked Mobile Number", "aadhaarNumber": "<PERSON><PERSON><PERSON><PERSON>", "aadhaarVerificationFailed": "Verification Failed", "aadhaarVerifiedSuccessfully": "Verified Successfully", "aadharUidNotAvailable": "Aadhar/ UID Not Available", "academicDetails": "Academic Details", "academicYear": "Academic Year", "academicYearRequired": "Academic year is required", "accountCreated": "Account Created", "accountCreatedMessage": "Signup successful! Use your mobile number or email ID to log in.", "accountCreatedTitle": "User Account Created", "accountHolderName": "Name of the Account Holder", "accountHolderNameRequired": "Account holder name is required", "accountNumber": "Account Number", "accountNumberRequired": "Account number is required", "accountVerified": "Account Verified", "achievementCertificate": "Sports/Arts Achievement Certificate", "acknowledgment": "Acknowledgement", "action": "Action", "actions": "Actions", "addMore": "Add More", "additionalDetails": "Additional Details (If applicable)", "address": "Address", "advancedSearch": "Advanced Search", "age": "Age", "ageUnit": "Age Unit", "agreeAndContinue": "Agree and Continue", "aided": "Aided", "alert": "<PERSON><PERSON>", "alreadyRegistered": "Already registered?", "alreadyRegisteredText": "Already registered?", "annualFamilyIncome": "Annual Family Income (in ₹)", "applicant": "Applicant", "applicantCareStatus": "Applicant is under the care of", "applicantDetails": "Applicant Details", "applicantType": "Applicant Type", "applicantUnderCareOf": "Applicant is under the care of", "application": "Application", "applicationNo": "Application No", "applicationNumber": "Application Number", "applicationSubmissionFailed": "Failed to submit application. Please try again.", "applicationSubmittedSuccessfully": "Application submitted successfully!", "applied": "Applied", "appliedDate": "Applied Date", "apply": "Apply", "approved": "Approved", "areBothParentsBedridden": "Are both parents bedridden/terminally ill?", "assignee": "Assignee", "attachDocument": "Attach Document", "attachDocuments": "Attach Documents", "attachmentsOf": "Attachments of", "back": "Back", "backToLogin": "Back to Login", "bankAccountRequirement": "To apply for the scholarship, you must have a bank account in your own name. Please enter your bank account details below.", "bankDetails": "Bank Details", "bankName": "Bank Name", "bankNameRequired": "Bank name is required", "bankPassbook": "Bank Passbook", "bankPassbookDescription": "For account verification", "board": "Board", "boardRequired": "Board is required", "branch": "Branch", "branchName": "Branch Name", "branchRequired": "Branch is required", "cancel": "Cancel", "ccpaDetailsIfApplicable": "CCPA Details (If Applicable )", "certificate": "Certificate", "certificateIssuedDate": "Certificate Issued Date", "certificateKeyNo": "Certificate No/ Key No", "certificatePreview": "Certificate Preview", "cgpa": "CGPA", "cgpaOgpa": "CGPA / OGPA", "cgpaOgpaDetails": "CGPA / OGPA Details", "cgpaPercentage": "CGPA/Percentage", "changeFile": "Change File", "changePassword": "Change Password", "cityTown": "City/Town", "class10BoardExam": "Class 10 Board Exam", "class10BoardExamDetails": "Class 10 Board Exam Details", "competitiveExam": "Competitive Exam (All India Level)", "competitiveExamAllIndiaLevel": "Competitive Exam All India Level", "completeRegistrationMessage": "Complete your registration process", "completed": "Completed", "completedCourseMarklist": "Completed Course Mark-list", "confirm": "Confirm", "confirmApplication": "Confirm Application", "confirmPassword": "Confirm Password", "consentRequired": "You must agree to the consent terms to proceed", "consentTitle": "Aadhaar Verification Consent", "contactNumber": "Contact Number", "correspondence": "Correspondence", "correspondenceModeNotAllowed": "Correspondence mode is not allowed. Only regular mode is permitted.", "country": "Country", "courseMode": "Course Mode", "courseModeRequired": "Course mode is required", "courseName": "Course Name", "createAccount": "Create Your Account", "createAccountTitle": "Create Your Account", "createNewPassword": "Create a new password for your account", "createYourAccount": "Create Your Account", "currentCareProvider": "Current Care Provider", "currentCourse": "Current Course", "currentCourseCertificate": "Current Course Certificate", "currentCourseDetails": "Current Course Details", "currentInstitutionNameRequired": "Current institution name is required", "currentInstitutionType": "Current Institution Type", "currentInstitutionTypeRequired": "Current institution type is required", "dateCannotBeFuture": "Date cannot be in the future", "dateOfAdmission": "Date of Admission", "dateOfAdmissionRequired": "Date of admission is required", "dateOfBirth": "Date of Birth", "defaultSubtitle": "Always stay updated in your student portal", "degreeCertificate": "Degree Certificate", "degreeMarkList": "PG Marksheet", "degreeMarkListDescription": "For PG applicants only", "destituteCertificate": "Destitute Certificate", "disabilityCertificate": "Disability Certificate", "disabilityPercentage": "Disability Percentage", "disabilityStatus": "Disability Status", "district": "District", "districtOfInstitution": "District of Institution", "districtOfInstitutionRequired": "District of institution is required", "districtofInstitution": "District of Institution", "divorcedOrDeathCertificate": "Divorced/Death Certificate", "documentGuidelinesMessage": "All files should be in PDF or JPEG/JPG/PNG format and under 2MB each. Ensure documents are clear and readable.", "documentGuidelinesTitle": "Document Guidelines", "documentUpload": "Document Upload", "documents": "Documents", "documentsUpload": "Documents Upload", "draft": "Draft", "edit": "Edit", "eligibilityAlert": "Eligibility Alert", "email": "Email", "emailAddressMobileNumber": "Email Address Mobile Number", "emailId": "Email ID", "emailOrMobile": "Email Or Mobile", "enter": "Enter", "enterEmailOrMobile": "Enter Email Or Mobile", "enterField": "Enter {{field}}", "enterHere": "Enter here", "enterSelectField": "Enter/Select {{field}}", "error": "Error", "failedToSendOtp": "Failed to send OTP", "familyCircumstances": "Family Circumstances", "familyIncomeCriteria": "Family income must be ≤ ₹2.5 lakhs per annum to be eligible for the scholarship", "father": "Father", "fatherMedicalCertificate": "Father Medical Certificate", "fieldValidEmailOrMobile": "Email Or Mobile is required", "filter": "Filter", "financialDetails": "Financial Details", "financialInformation": "Financial Information", "firstName": "First Name", "forgotPassword": "Forgot Password", "gender": "Gender", "getOTP": "Get OTP", "goBack": "Go back", "goToLoginButton": "Go to Login", "government": "Government", "grade": "Grade", "gradePercentage": "Grade/Percentage", "gradeRequired": "Grade/Percentage is required", "gradingSystem": "Grading System", "guardian": "Guardian", "guardianDetails": "Guardian Details", "hasRepresentedAtStateLevel": "Have you represented at the State Level in Sports/Arts?", "higherSecondary": "Higher Secondary (HSS)", "higherSecondaryDetails": "Higher Secondary Details", "higherSecondaryExam": "Higher Secondary Exam", "houseNumber": "House Number & Name", "hsBoard": "Higher Secondary Board", "hsDistrictOfInstitution": "Higher Secondary District of Institution", "hsGradePercentage": "Higher Secondary Grade/Percentage", "hsInstitutionName": "Higher Secondary Institution Name", "hsRegisterNumber": "Hs Register Number", "hsStateOfInstitution": "Higher Secondary State of Institution", "hsYearOfCompletion": "Higher Secondary Year of Completion", "hssSubtitle": "For students learning in class 11", "ifApplicable": "If applicable", "ifsc": "IFSC", "ifscCode": "IFSC Code", "ifscRequired": "IFSC code is required", "importantNote": "Important Note", "incomeCertificate": "Income Certificate", "incomeCertificateDescription": "Official family income certificate issued in parent/guardian name", "incomeCertificateIssuedBy": "Income Certificate Issued by", "incomeCertificateNo": "Income Certificate No", "institution": "Institution", "institutionAddress": "Institution Address", "institutionContactNumber": "Institution Contact Number", "institutionDetails": "Institution Details", "institutionLocation": "Institution Location", "institutionLocationRequired": "Institution location is required", "institutionName": "Institution Name", "institutionNamePlaceholder": "Institution name", "institutionNameRequired": "Institution name is required", "institutionRegistrationNumber": "Institution Registration Number", "institutionType": "Institution Type", "institutionTypeRequired": "Institution type is required", "instructionLocation": "Instruction Location", "instructionName": "Instruction Name", "instructionType": "Instruction Type", "invalidCredentials": "Invalid credentials", "invalidIFSCFormat": "Invalid IFSC format (e.g., ABCD0123456)", "isDifferentlyAbled": "Are you a Differently Abled Candidate (60% disability or more)?", "isFromSingleParentHousehold": "Are you from a Single Parent Household?", "isNriParent": "Is this applicant a ward of an NRI parent?", "isOrphan": "Are you an Orphan?", "isResident": "Are you a Keralite?", "isSingleParentHousehold": "Do you belong to a single-parent household with a bedridden parent?", "keralaOriginRequired": "Only candidates of Kerala origin are eligible for this scholarship", "lastName": "Last Name", "loginFailed": "Login Failed", "loginSuccessful": "Login successful!", "mark": "<PERSON>", "marksDetails": "Marks Details", "marksObtained": "Marks Obtained", "masterGraduation": "Post Graduation (PG)", "max18Digits": "Maximum 18 digits allowed", "max50Characters": "Maximum 50 characters allowed", "max100Characters": "Maximum 100 characters allowed", "middleName": "Middle Name", "min3Characters": "Minimum 3 characters required", "min9Digits": "Minimum 9 digits required", "mobileNo": "Mobile Number", "mobileNumber": "Mobile Number", "mother": "Mother", "motherMedicalCertificate": "Mother Medical Certificate", "myApplications": "My Applications", "name": "Name", "nameOfInstitution": "Name of Institution", "nativityCertificate": "Nativity Certificate", "newApplication": "New Application", "newApplications": "New Applications", "newPassword": "New Password", "next": "Next", "nextButton": "Next", "nextStep": "Next Step", "noAccount": "No Account", "noDataFound": "No data found", "noDocumentsFound": "No Documents Found", "norkaId": "Norka ID", "notFound": "Sorry, the page or resource you are looking for could not be found.", "onlyLettersAllowed": "Only letters are allowed", "onlyNumbersAllowed": "Only numbers are allowed", "onlyNumbersAndDecimalAllowed": "Only numbers and decimal point allowed", "others": "Others", "otp": "OTP", "otpLengthError": "OTP must be 6 digits", "otpResentMessage": "A new OTP has been sent to your registered email/phone", "otpResentTitle": "OTP Resent", "otpSentMessage": "OTP has been sent to your registered contact", "otpVerificationInstruction": "Please complete the OTP verification by entering the code sent to your registered email ID or phone number", "pageNotFound": "Page Not Found", "parent": "Parent", "parentDetails": "Parent Details", "parentGuardianDetails": "Parent / Guardian Details", "parentGuardianInfo": "Parent/Guardian Info", "parentName": "Parent Name", "passportPhoto": "Passport Size Photograph", "password": "Password", "passwordChanged": "Password Changed", "passwordUpdatedSuccessfully": "Password updated successfully", "percentage": "Percentage", "percentageOfDisability": "Percentage of Disability", "permanentAddress": "Permanent Address", "permissionDenied": "Permission Denied", "personalDetails": "Personal Details", "pgSubtitle": "For postgraduate and master's degree programs", "photoDescription": "Recent passport-size photograph", "pincode": "Pincode", "pleaseSelectOption": "Please Select an Option", "pleaseTryAgain": "Sorry, something went wrong. Please try again later.", "pravasiIdCard": "Pravasi ID Card", "pravasiIdCardNumber": "Pravasi ID Card Number", "previous": "Previous", "processing": "Processing", "rationCard": "Ration Card Copy", "rationCardNumber": "Ration Card Number", "registerNumber": "Register Number", "registrationCompletionText": "To complete your registration, please fill in all the fields below", "registrationNumber": "Registration Number", "regular": "Regular", "relation": "Relation", "relationshipCertificate": "Relationship to Student Certificate", "relationshipToApplicant": "Relationship to Applicant", "resendCode": "Resend Code", "resendOTP": "Resend OTP", "resendOTPButton": "Resend OTP", "reviewSubmit": "Review & Submit", "save": "Save", "saving": "Saving...", "scholarshipType": "Scholarship Type", "search": "Search", "select": "Select", "selectField": "Select {{field}}", "selfFinanced": "Self-Financed", "sendOtpSuccess": "O<PERSON> Successfully", "serverError": "Sorry, something went wrong. Please try again later.", "serviceUnavailable": "Sorry, the service is currently unavailable. Please try again later.", "signIn": "Sign In", "signInLink": "Sign in", "signInToAccess": "Sign in to access your account", "signingIn": "Signing In...", "slNo": "Sl No", "sourceOfIncome": "Source(s) of Income", "state": "State", "stateOfInstitution": "State of Institution", "stateOfInstitutionRequired": "State of institution is required", "stateofInstitution": "State of Institution", "stream": "Stream", "streetLocality": "Street/Locality", "student": "Student", "studentName": "Student Name", "studentSignature": "Student Signature", "submit": "Submit", "submitApplication": "Submit Application", "submitApplicationConfirmMessage": "By clicking 'CONFIRMATION', you are submitting your {{scholarshipType}} NORKA Scholarship application. Please note that once submitted, the application cannot be edited or deleted. Are you sure you want to proceed?", "success": "Success", "successIconAlt": "Success tick icon", "tenthCertificate": "10th Certificate", "tenthMarkList": "STATE/CBSE/ICSE SSLC Marksheet", "tenthMarkListDescription": "For HSS applicants only", "totalMarks": "Total Marks", "ugSubtitle": "For undergraduate degree programs and courses", "unAuthorized": "Un-Authorized", "underGradCourseName": "Under Graduate Course Name", "underGradDistrictOfInstitution": "Under Graduate District of Institution", "underGradStateOfInstitution": "Under Graduate State of Institution", "underGradYearOfCompletion": "Under Graduate Year of Completion", "underGraduate": "Under Graduation (UG)", "underGraduationDetails": "Under Graduation Details", "university": "University", "updatePassword": "Update Password", "updating": "Updating...", "upload": "Upload", "userName": "User Name", "validPercentageRequired": "Please enter a valid percentage (0-100)", "verificationCode": "Verification Code", "verificationSuccessMessage": "Your account has been created successfully!", "verificationSuccessTitle": "Verification Successful", "verificationSuccessful": "Verification successful!", "verify": "Verify", "verifyOtpSuccess": "OTP Verified Successfully", "verifying": "Verifying...", "6": "6", "documentsWillAppearHere": "Documents will appear here once uploaded", "noAadhaarVaultId": "Kindly verify your <PERSON><PERSON><PERSON><PERSON> Number before you submit the form", "aadhaarNameMismatch": "The name entered does not match the name on the Aadhaar. Please ensure the name matches exactly as per Aadhaar records.", "scholarshipApplication": "Scholarship Application", "verifyingText": "Verifying...", "view": "View", "welcome": "Welcome!", "welcomeBack": "Welcome Back", "yearOfCompletion": "Year of Completion", "yearOfCompletionRequired": "Year of completion is required", "yearOfStudy": "Year of Study", "yearofCompletion": "Year of Completion", "yourName": "Your Name"}