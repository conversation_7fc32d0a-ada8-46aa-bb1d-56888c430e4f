import React, { useEffect, useMemo } from 'react';
import { useForm, FormProvider } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { useDispatch, useSelector } from 'react-redux';
import { Box, t } from 'common/components';
import { StepperButtons } from 'common/custom-components';
import { useFetchDistrictQuery, useFetchStateQuery } from 'pages/common/api';
import { STATE } from 'common/constants';
import { actions as commonActions } from 'pages/common/slice';
import { getScholarshipType } from 'pages/common/selectors';
import { academicDetailsSchema } from '../validation/academicDetails';
import {
  useGetAcademicDetailsQuery,
  useGetApplicationDetailsQuery,
  useGetBoardQuery,
  useGradeOptionsQuery,
  useSaveAcademicDetailsMutation,
  useUpdateAcademicDetailsMutation
} from '../api';
import { getApplicationId } from '../selectors';
import {
  buildFormData, transformAcademicDetails, transformHsAcademicDetails, transformUgAcademicDetails
} from '../helpers';

// Import components
import Class10AcademicDetails from './Class10AcademicDetails';
import HigherSecondaryAcademicDetails from './HigherSecondaryAcademicDetails';
import UndergraduateAcademicDetails from './UndergraduateAcademicDetails';
import CurrentCourseDetails from './CurrentCourseDetails';

const AcademicDetails = ({ onNext, onPrevious }) => {
  const [saveAcademicDetails, { isLoading: isSaveLoading }] = useSaveAcademicDetailsMutation();
  const [
    updateAcademicDetails,
    { isLoading: isUpdateLoading }
  ] = useUpdateAcademicDetailsMutation();
  const dispatch = useDispatch();

  const isLoading = isSaveLoading || isUpdateLoading;
  const applicationId = useSelector(getApplicationId);
  const scholarshipType = useSelector(getScholarshipType);

  // Fetch application and academic data
  const { data: applicationDetails } = useGetApplicationDetailsQuery(applicationId, {
    skip: !applicationId
  });

  const educationQualificationID = applicationDetails?.payload?.educationQualification?.id;
  const { data: boardData } = useGetBoardQuery(educationQualificationID, {
    skip: !educationQualificationID
  });

  const { data: academicDetails } = useGetAcademicDetailsQuery(applicationId, {
    skip: !applicationId
  });

  // Fetch location data
  const { data: districtOptions = [] } = useFetchDistrictQuery(STATE.id);
  const { data: stateOptions = [], isLoading: stateLoading } = useFetchStateQuery(STATE.id);

  // Transform board data
  const boardOptions = useMemo(() => {
    if (!boardData?.payload) return [];
    return boardData.payload.map((board) => ({
      code: board.code,
      name: board.name,
      id: board.id
    }));
  }, [boardData?.payload]);
  const getCurrentYear = () => new Date().getFullYear().toString();
  const { data: gradeOptions } = useGradeOptionsQuery({
    educationQualificationId: educationQualificationID,
    boardId: academicDetails?.board?.id
  });

  // Form setup
  const methods = useForm({
    mode: 'onChange',
    resolver: yupResolver(academicDetailsSchema),
    defaultValues: {
      state: STATE.id,
      currentInstitutionType: '',
      currentInstitutionName: '',
      courseMode: '',
      academicYear: '',
      dateOfAdmission: '',
      board: '',
      gradePercentage: '',
      institutionName: '',
      institutionType: '',
      institutionLocation: '',
      yearOfCompletion: getCurrentYear,
      streamCriteria: '',
      stateOfInstitution: '',
      districtOfInstitution: '',
      marksObtained: '',
      totalMarks: '',
      percentage: '',
      hsBoard: '',
      hsGradePercentage: '',
      hsInstitutionName: '',
      hsYearOfCompletion: getCurrentYear,
      hsStateOfInstitution: '',
      hsDistrictOfInstitution: '',
      underGradCourseName: '',
      instructionType: '',
      instructionLocation: '',
      instructionName: '',
      competitiveExam: '',
      underGradYearOfCompletion: getCurrentYear,
      underGradStateOfInstitution: '',
      underGradDistrictOfInstitution: '',
      underGradGradingSystem: '',
      underGradMarksOrScore: '',
      underGradTotalMarks: '',
      underGradPercentage: ''
    }
  });

  const {
    watch, setValue, reset, handleSubmit
  } = methods;

  // Watch course mode for correspondence alert
  const courseMode = watch('courseMode');
  useEffect(() => {
    if (courseMode === 'correspondence') {
      dispatch(commonActions.setCustomToast({
        open: true,
        variant: 'warning',
        title: t('courseMode'),
        message: t('correspondenceModeNotAllowed')
      }));
      setValue('courseMode', 'regular');
    }
  }, [courseMode, dispatch, setValue, t]);

  // Reset form with fetched data
  useEffect(() => {
    if (academicDetails && boardOptions.length > 0) {
      const apiData = academicDetails;
      const selectedBoard = boardOptions.find((board) => board.id === apiData.board?.id);
      const boardCode = selectedBoard ? selectedBoard.code : '';

      const formData = buildFormData(apiData, boardCode, STATE, getCurrentYear);

      reset(formData);
    } else {
      setValue('state', STATE.id);
      setValue('hsState', STATE.id);
    }
  }, [academicDetails, boardOptions, reset, setValue]);

  const onSubmit = async (data) => {
    let transformedData;
    if (scholarshipType === 'higherSecondary') {
      transformedData = transformAcademicDetails({
        ...data,
        applicationId
      }, scholarshipType);
    } else if (scholarshipType === 'underGraduate') {
      transformedData = transformHsAcademicDetails({
        ...data,
        applicationId
      }, scholarshipType);
    } else if (scholarshipType === 'masterGraduation') {
      transformedData = transformUgAcademicDetails({
        ...data,
        applicationId
      }, scholarshipType);
    }

    if (!transformedData) {
      return;
    }

    try {
      if (academicDetails?.id) {
        await updateAcademicDetails({
          id: academicDetails.id,
          ...transformedData
        }).unwrap();
      } else {
        await saveAcademicDetails(transformedData).unwrap();
      }

      if (onNext) onNext(data);
    } catch (error) {
      dispatch(commonActions.setCustomToast({
        open: true,
        variant: 'error',
        title: 'Error',
        message: 'Failed to save academic details'
      }));
    }
  };

  const renderAcademicSections = () => {
    switch (scholarshipType) {
      case 'higherSecondary':
        return (
          <>
            <Class10AcademicDetails
              stateOptions={stateOptions}
              districtOptions={districtOptions}
              boardOptions={boardOptions}
              stateLoading={stateLoading}
              gradeOptions={gradeOptions}
            />
            <CurrentCourseDetails />
          </>
        );
      case 'underGraduate':
        return (
          <>
            <HigherSecondaryAcademicDetails
              stateOptions={stateOptions}
              districtOptions={districtOptions}
              boardOptions={boardOptions}
              stateLoading={stateLoading}
            />
            <CurrentCourseDetails />
          </>
        );
      case 'masterGraduation':
        return (
          <>
            <UndergraduateAcademicDetails
              // stateOptions={stateOptions}
              // districtOptions={districtOptions}
              boardOptions={boardOptions}
              // stateLoading={stateLoading}
              // gradeOptions={gradeOptions}
            />
            <CurrentCourseDetails />
          </>
        );
      default:
        return null;
    }
  };

  return (
    <FormProvider {...methods}>
      <Box gap={10} as="form" onSubmit={handleSubmit(onSubmit)}>
        {renderAcademicSections()}
        <Box mt={8}>
          <StepperButtons
            currentStep={3}
            totalSteps={6}
            onNext={handleSubmit(onSubmit)}
            onPrevious={onPrevious}
            layout="space-between"
            nextButtonProps={{
              isLoading,
              loadingText: 'Saving...'
            }}
          />
        </Box>
      </Box>
    </FormProvider>
  );
};

export default AcademicDetails;
