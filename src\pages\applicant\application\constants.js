// === State Reducer Key ===
export const STATE_REDUCER_KEY = {
  SLICE: 'application',
  API: 'application-api'
};

// === Bank Options ===
export const BANK_OPTIONS = [
  { code: 'sbi', name: 'State Bank of India' },
  { code: 'hdfc', name: 'HDFC Bank' },
  { code: 'icici', name: 'ICICI Bank' },
  { code: 'axis', name: 'Axis Bank' },
  { code: 'pnb', name: 'Punjab National Bank' }
];

// === Branch Options ===
export const BRANCH_OPTIONS = [
  { code: 'branch1', name: 'Main Branch' },
  { code: 'branch2', name: 'City Center Branch' }
];

// === Board Options ===
// === Institution T
export const BOARD_OPTIONS = [
  { code: 'cbse', name: 'CBSE' },
  { code: 'state', name: 'State Board' },
  { code: 'icse', name: 'ICSE' },
  { code: 'ib', name: 'IB' }
];

// === Institution Type Options ===
export const INSTITUTION_TYPE_OPTIONS = [
  { code: 'GOVERNMENT', name: 'Government' },
  { code: 'AIDED', name: 'Aided' },
  { code: 'SELF_FINANCE', name: 'Self Finance' },
  { code: 'EDUCATIONAL_RESEARCH', name: 'Educational Research' },
  { code: 'SCIENTIFIC', name: 'Scientific Institutions' }
];

// === Year Options ===
export const YEAR_OPTIONS = [
  { code: '2025', name: '2025' }
];

// === Academic Year Options ===
export const ACADEMIC_YEAR_OPTIONS = [
  { code: '2024-25', name: '2024-25' }
];

// === Stepper Steps ===
export const STEPPER_STEPS = {
  APPLICANT_DETAILS: 'APPLICANT_DETAILS',
  PARENT_DETAILS: 'PARENT_DETAILS',
  BANK_DETAILS: 'BANK_DETAILS',
  ACADEMIC_DETAILS: 'ACADEMIC_DETAILS',
  DOCUMENTS_UPLOAD: 'DOCUMENTS_UPLOAD',
  REVIEW_SUBMIT: 'REVIEW_SUBMIT'
};

// Care status enum
export const CARE_STATUS = {
  PARENTS: 'PARENTS',
  SINGLE_PARENT: 'SINGLE_PARENT',
  ORPHAN: 'ORPHAN'
};

export const CARE_STATUS_OPTIONS = [
  { code: CARE_STATUS.PARENTS, name: 'Under The Care Of Parents' },
  { code: CARE_STATUS.SINGLE_PARENT, name: 'Under The Care Of A Single Parent' },
  { code: CARE_STATUS.ORPHAN, name: 'Orphan (No Living Parents)' }
];

// === Current Care Provider Enum & Options ===
export const CURRENT_CARE_PROVIDER = {
  GUARDIAN: 'GUARDIAN',
  INSTITUTION: 'INSTITUTION'
};

export const CURRENT_CARE_PROVIDER_OPTIONS = [
  { code: CURRENT_CARE_PROVIDER.GUARDIAN, name: 'Guardian' },
  { code: CURRENT_CARE_PROVIDER.INSTITUTION, name: 'Institution' }
];

// === Income Certificate Issuer Enum & Options ===
export const INCOME_CERTIFICATE_ISSUER = {
  TAHSILDAR: 'TAHSILDAR',
  VILLAGE_OFFICER: 'VILLAGE_OFFICER'
};

export const INCOME_CERTIFICATE_ISSUER_OPTIONS = [
  { code: INCOME_CERTIFICATE_ISSUER.TAHSILDAR, name: 'Tahsildar' },
  { code: INCOME_CERTIFICATE_ISSUER.VILLAGE_OFFICER, name: 'Village Officer' }
];

// === Relationship Enum & Options ===
export const RELATIONSHIP = {
  FATHER: 'FATHER',
  MOTHER: 'MOTHER'
};

export const RELATIONSHIP_OPTIONS = [
  { code: RELATIONSHIP.FATHER, name: 'Father' },
  { code: RELATIONSHIP.MOTHER, name: 'Mother' }
];

// Family circumstances enum
export const FAMILY_CIRCUMSTANCES = {
  ORPHAN: 'ORPHAN',
  SINGLE_PARENT_BEDRIDDEN: 'SINGLE_PARENT_BEDRIDDEN',
  SINGLE_PARENT_HOUSEHOLD: 'SINGLE_PARENT_HOUSEHOLD',
  BOTH_PARENTS_BEDRIDDEN: 'BOTH_PARENTS_BEDRIDDEN',
  NOT_APPLICABLE: 'NOT_APPLICABLE'
};

export const FAMILY_CIRCUMSTANCES_OPTIONS = [
  { code: FAMILY_CIRCUMSTANCES.ORPHAN, name: 'Orphan' },
  { code: FAMILY_CIRCUMSTANCES.SINGLE_PARENT_BEDRIDDEN, name: 'Single Parent Bedridden/Terminally Ill' },
  { code: FAMILY_CIRCUMSTANCES.SINGLE_PARENT_HOUSEHOLD, name: 'Single Parent Household' },
  { code: FAMILY_CIRCUMSTANCES.BOTH_PARENTS_BEDRIDDEN, name: 'Both Parents Bedridden/Terminally Ill' },
  { code: FAMILY_CIRCUMSTANCES.NOT_APPLICABLE, name: 'Not Applicable' }
];

export const COURSE_NAME_OPTIONS = [
  { code: 'ba', name: 'Bachelor of Arts (BA)' },
  { code: 'bsc', name: 'Bachelor of Science (BSc)' },
  { code: 'bcom', name: 'Bachelor of Commerce (BCom)' },
  { code: 'bba', name: 'Bachelor of Business Administration (BBA)' },
  { code: 'bpharm', name: 'Bachelor of Pharmacy (BPharm)' }
];

export const INSTRUCTION_TYPE_OPTIONS = [
  { code: 'regular', name: 'Regular' },
  { code: 'distance', name: 'Distance Education' },
  { code: 'online', name: 'Online' },
  { code: 'part-time', name: 'Part-time' },
  { code: 'correspondence', name: 'Correspondence' }
];

export const GRADE_OPTIONS = [
  { code: 'Marks', name: 'MARKS' },
  { code: 'CGPA', name: 'CGPA' }
];

export const DOCUMENT_DATA = {
  PHOTO: 'PHOTO',
  STUDENT_AADHAAR: 'STUDENT_AADHAAR',
  '10TH_CERTIFICATE': '10TH_CERTIFICATE',
  '10TH_MARKLIST': '10TH_MARKLIST',
  INCOME_CERTIFICATE: 'INCOME_CERTIFICATE',
  BANK_PASSBOOK_FIRST_PAGE: 'BANK_PASSBOOK_FIRST_PAGE',
  APPLICANT_SIGNATURE: 'APPLICANT_SIGNATURE',
  COMPLETED_COURSE_MARKLIST: 'COMPLETED_COURSE_MARKLIST',
  CURRENT_COURSE_CERTIFICATE: 'CURRENT_COURSE_CERTIFICATE',
  NATIVITY_CERTIFICATE: 'NATIVITY_CERTIFICATE',
  // Disability related attachments
  DISABILITY_CERTIFICATE: 'DISABILITY_CERTIFICATE',
  // Guardian/Parent related attachments
  GUARDIAN_AADHAAR: 'GUARDIAN_AADHAAR',
  RELATIONSHIP_CERTIFICATE: 'RELATIONSHIP_CERTIFICATE',
  DESTITUTE_CERTIFICATE: 'DESTITUTE_CERTIFICATE',
  DIVORCED_CERTIFICATE_OR_DEATH_CERTIFICATE: 'DIVORCED_CERTIFICATE_OR_DEATH_CERTIFICATE',
  PARENT_AADHAAR: 'PARENT_AADHAAR',
  MOTHER_AADHAAR: 'MOTHER_AADHAAR',
  FATHER_AADHAAR: 'FATHER_AADHAAR',
  MEDICAL_CERTIFICATE_MOTHER: 'MEDICAL_CERTIFICATE_MOTHER',
  MEDICAL_CERTIFICATE_FATHER: 'MEDICAL_CERTIFICATE_FATHER',
  // Achievement related attachments
  SPORTS_ARTS_CERTIFICATE: 'SPORTS_ARTS_CERTIFICATE',
  // NRI related attachments
  PRAVASI_ID_CARD: 'PRAVASI_ID_CARD',
  // Education qualification related attachments
  PG_MARKLIST: 'PG_MARKLIST',
  DEGREE_CERTIFICATE: 'DEGREE_CERTIFICATE',
  DEGREE_MARKLIST: 'DEGREE_MARKLIST',
  HSS_CERTIFICATE: 'HSS_CERTIFICATE',
  HSS_MARKLIST: 'HSS_MARKLIST'
};

export const DOCUMENT_META_DATA = {
  [DOCUMENT_DATA.PHOTO]: {
    name: 'photo',
    label: 'passportPhoto',
    description: 'photoDescription'
  },
  [DOCUMENT_DATA.STUDENT_AADHAAR]: {
    name: 'studentAadhaar',
    label: 'studentAadhaar',
    description: 'aadhaarDescription'
  },
  [DOCUMENT_DATA['10TH_CERTIFICATE']]: {
    name: 'tenthCertificate',
    label: 'tenthCertificate',
    description: 'tenthCertificateDescription'
  },
  [DOCUMENT_DATA['10TH_MARKLIST']]: {
    name: 'tenthMarkList',
    label: 'tenthMarkList',
    description: 'tenthMarkListDescription'
  },
  [DOCUMENT_DATA.INCOME_CERTIFICATE]: {
    name: 'incomeCertificate',
    label: 'incomeCertificate',
    description: 'incomeCertificateDescription'
  },
  [DOCUMENT_DATA.BANK_PASSBOOK_FIRST_PAGE]: {
    name: 'bankPassbook',
    label: 'bankPassbook',
    description: 'bankPassbookDescription'
  },
  [DOCUMENT_DATA.APPLICANT_SIGNATURE]: {
    name: 'studentSignature',
    label: 'studentSignature',
    description: 'forVerification'
  },
  [DOCUMENT_DATA.COMPLETED_COURSE_MARKLIST]: {
    name: 'completedCourseMarklist',
    label: 'completedCourseMarklist',
    description: 'completedCourseMarklistDescription'
  },
  [DOCUMENT_DATA.CURRENT_COURSE_CERTIFICATE]: {
    name: 'currentCourseCertificate',
    label: 'currentCourseCertificate',
    description: 'currentCourseCertificateDescription'
  },
  [DOCUMENT_DATA.NATIVITY_CERTIFICATE]: {
    name: 'nativityCertificate',
    label: 'nativityCertificate',
    description: 'nativityCertificateDescription'
  },
  [DOCUMENT_DATA.DIVORCED_CERTIFICATE_OR_DEATH_CERTIFICATE]: {
    name: 'divorcedOrDeathCertificate',
    label: 'divorcedOrDeathCertificate',
    description: 'divorcedOrDeathCertificateDescription'
  },
  [DOCUMENT_DATA.DISABILITY_CERTIFICATE]: {
    name: 'disabilityCertificate',
    label: 'disabilityCertificate',
    description: 'disabilityCertificateDescription'
  },
  [DOCUMENT_DATA.RELATIONSHIP_CERTIFICATE]: {
    name: 'relationshipCertificate',
    label: 'relationshipCertificate',
    description: 'relationshipCertificateDescription'
  },
  [DOCUMENT_DATA.DESTITUTE_CERTIFICATE]: {
    name: 'destituteCertificate',
    label: 'destituteCertificate',
    description: 'destituteCertificateDescription'
  },
  [DOCUMENT_DATA.PARENT_AADHAAR]: {
    name: 'parentAadhaar',
    label: 'parentAadhaar',
    description: 'aadhaarDescription'
  },
  [DOCUMENT_DATA.MOTHER_AADHAAR]: {
    name: 'motherAadhaar',
    label: 'motherAadhaar',
    description: 'aadhaarDescription'
  },
  [DOCUMENT_DATA.FATHER_AADHAAR]: {
    name: 'fatherAadhaar',
    label: 'fatherAadhaar',
    description: 'aadhaarDescription'
  },
  [DOCUMENT_DATA.GUARDIAN_AADHAAR]: {
    name: 'guardianAadhaar',
    label: 'guardianAadhaar',
    description: 'aadhaarDescription'
  },
  [DOCUMENT_DATA.MEDICAL_CERTIFICATE_MOTHER]: {
    name: 'motherMedicalCertificate',
    label: 'motherMedicalCertificate',
    description: 'motherMedicalCertificateDescription'
  },
  [DOCUMENT_DATA.MEDICAL_CERTIFICATE_FATHER]: {
    name: 'fatherMedicalCertificate',
    label: 'fatherMedicalCertificate',
    description: 'fatherMedicalCertificateDescription'
  },
  [DOCUMENT_DATA.SPORTS_ARTS_CERTIFICATE]: {
    name: 'sportsOrArtsCertificate',
    label: 'achievementCertificate',
    description: 'ifApplicable'
  },
  [DOCUMENT_DATA.PRAVASI_ID_CARD]: {
    name: 'pravasiIdCard',
    label: 'pravasiIdCard',
    description: 'pravasiIdCardDescription'
  },
  [DOCUMENT_DATA.DEGREE_CERTIFICATE]: {
    name: 'degreeCertificate',
    label: 'degreeCertificate',
    description: 'degreeCertificateDescription'
  },
  [DOCUMENT_DATA.DEGREE_MARKLIST]: {
    name: 'degreeMarkList',
    label: 'degreeMarkList',
    description: 'degreeMarkListDescription'
  },
  [DOCUMENT_DATA.HSS_CERTIFICATE]: {
    name: 'hssCertificate',
    label: 'hssCertificate',
    description: 'hssCertificateDescription'
  },
  [DOCUMENT_DATA.HSS_MARKLIST]: {
    name: 'hssMarkList',
    label: 'hssMarkList',
    description: 'hssMarkListDescription'
  }
};

export const COURSE_MODE = [
  { code: 'REGULAR', name: 'REGULAR' },
  { code: 'CORRESPONDENCE', name: 'CORRESPONDENCE' }
];

export const GRADES = [
  { code: 'FullA+', name: 'FULL A+' }
];

export const PERCENTAGE_OPTIONS = [
  { code: 'GreaterThan90', name: 'GREATER THAN 90%' }
];

export const GRADING_OPTIONS = [
  { code: 'MARK', name: 'MARK' },
  { code: 'CGPA', name: 'CGPAOGPA' }
];

export const MARK = 'MARK';
export const CGPA = 'CGPA';
