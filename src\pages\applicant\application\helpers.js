import { YES_OR_NO } from 'pages/common/constant';
import { formatDateWithDayjs } from 'utils/date';
import { selectorWithKey } from 'utils/common';
import { _ } from 'utils/lodash';
import { FAMILY_CIRCUMSTANCES, CARE_STATUS, CURRENT_CARE_PROVIDER } from './constants';

const transformHelpers = {
  toBool: (value) => value === YES_OR_NO.YES,
  parseInteger: (value) => {
    if (!value) return null;
    const parsed = parseInt(value, 10);
    return Number.isNaN(parsed) ? null : parsed;
  }
};

/**
 * Get uidVault from OTP verification state for a given Aadhaar number
 * @param {string} aadhaarNumber - The Aadhaar number to get uidVault for
 * @param {Object} otpState - The OTP state from Redux store
 * @returns {string|null} - The uidVault if available, otherwise null
 */
const getUidVaultFromOtpState = (aadhaarNumber, otpState) => {
  if (!aadhaarNumber || !otpState) return null;

  const aadhaarOtpData = selectorWithKey(otpState, aadhaarNumber) || {};
  const verificationData = aadhaarOtpData.verificationData || {};

  return _.get(verificationData, 'uidVault') || null;
};

/**
 * Get the name associated with the given Aadhaar number from OTP verification state
 * @param {string} aadhaarNumber - The Aadhaar number to get the name for
 * @param {Object} otpState - The OTP state from Redux store
 * @returns {string|null} - The name associated with the Aadhaar number, or null if not available
 */
export const getAadhaarNameFromOtpState = (aadhaarNumber, otpState) => {
  if (!aadhaarNumber || !otpState) return null;

  const aadhaarOtpData = selectorWithKey(otpState, aadhaarNumber) || {};
  return _.get(aadhaarOtpData.verificationData, 'name') || null;
};

/**
 * Get care status from family circumstances
 * @param {Object} applicationDetails - Application details object with family circumstances
 * @returns {string} - Care status code
 */
export const getCareStatusFromCircumstances = (applicationDetails) => {
  const {
    orphan,
    singleParentAndBedridden,
    singleParent,
    bothParentsAndBedridden
  } = applicationDetails || {};

  if (orphan) return CARE_STATUS.ORPHAN;
  if (singleParentAndBedridden || singleParent) return CARE_STATUS.SINGLE_PARENT;
  if (bothParentsAndBedridden) return CARE_STATUS.PARENTS;

  return CARE_STATUS.PARENTS;
};

/**
 * Check if entered name matches the Aadhaar-verified name
 * @param {string} enteredName - Name entered in the form
 * @param {string} aadhaarNumber - Aadhaar number
 * @param {Object} otpState - OTP verification state from Redux
 * @returns {boolean} - true if names match, false otherwise
 */
export const isNameMatchingAadhaarFromOtp = (enteredName, aadhaarNumber, otpState) => {
  if (!enteredName || !aadhaarNumber || !otpState) return false;

  const normalize = (name) => name.toLowerCase().replace(/\./g, '').replace(/\s+/g, ' ').trim();

  const aadhaarName = getAadhaarNameFromOtpState(aadhaarNumber, otpState);
  if (!aadhaarName) return false;

  return normalize(enteredName) === normalize(aadhaarName);
};

const getFamilyCircumstancesFlags = (familyCircumstances) => {
  const flags = {
    orphan: false,
    singleParentAndBedridden: false,
    singleParent: false,
    bothParentsAndBedridden: false
  };

  if (!familyCircumstances) return flags;

  switch (familyCircumstances) {
    case FAMILY_CIRCUMSTANCES.ORPHAN:
      flags.orphan = true;
      break;
    case FAMILY_CIRCUMSTANCES.SINGLE_PARENT_BEDRIDDEN:
      flags.singleParentAndBedridden = true;
      break;
    case FAMILY_CIRCUMSTANCES.SINGLE_PARENT_HOUSEHOLD:
      flags.singleParent = true;
      break;
    case FAMILY_CIRCUMSTANCES.BOTH_PARENTS_BEDRIDDEN:
      flags.bothParentsAndBedridden = true;
      break;
    default:
      break;
  }

  return flags;
};

const transformFormData = (formData, scholarshipTypeId, otpState = null) => {
  const {
    toBool, parseInteger
  } = transformHelpers;
  const familyFlags = getFamilyCircumstancesFlags(formData.familyCircumstances);

  // Get uidVault from OTP state if available, otherwise use aadhaarNumber
  const uidVault = getUidVaultFromOtpState(formData.aadhaarNumber, otpState);
  const aadhaarName = getAadhaarNameFromOtpState(formData.aadhaarNumber, otpState);

  return {
    firstName: formData.firstName ?? null,
    middleName: formData.middleName ?? null,
    lastName: formData.lastName ?? null,
    dateOfBirth: formatDateWithDayjs(formData.dateOfBirth),
    genderId: formData.gender,
    genderName: formData.gender,
    aadhaarVaultNo: uidVault ?? null,
    nameAsAadhaar: aadhaarName ?? null,
    houseNoName: formData.houseNumber ?? null,
    streetLocality: formData.streetLocality ?? null,
    cityTown: formData.cityTown ?? null,
    districtId: formData.district,
    districtName: formData.district,
    stateId: formData.state,
    pincode: parseInteger(formData.pincode),
    contactNumber: formData.mobileNumber ?? null,
    emailId: formData.emailId ?? null,
    keralite: toBool(formData.isResident),
    nriParent: toBool(formData.isNriParent),
    pravasiIdCardNumber: formData.pravasiIdCardNumber ?? null,
    studentDisable: toBool(formData.isDifferentlyAbled),
    disabilityPercentage: formData.percentageOfDisability ?? null,
    ...familyFlags,
    sportsArtsAchievement: toBool(formData.hasRepresentedAtStateLevel) ?? null,
    educationQualificationId: scholarshipTypeId
  };
};

const transformBankDetails = (formData) => {
  return {
    applicationId: formData.applicationId,
    accountHolderName: formData.accountHolderName,
    accountNumber: formData.accountNumber,
    bankName: formData.bankName,
    branchName: formData.branchName,
    ifscCode: formData.ifscCode
  };
};

/**
 * Transform form data to API payload format for parent/guardian details
 * @param {Object} formData - The form data from react-hook-form
 * @param {string} applicationId - The application ID
 * @param {Object} otpState - The OTP state from Redux store (optional)
 * @returns {Object} - Transformed payload for API
 */
const transformParentDetailsToPayload = (formData, applicationId, otpState = null) => {
  const { applicantCareStatus, currentCareProvider } = formData;

  const payload = {
    applicationId,
    parentOrGuardian: applicantCareStatus,
    guardian: currentCareProvider === CURRENT_CARE_PROVIDER.GUARDIAN,
    institution: currentCareProvider === CURRENT_CARE_PROVIDER.INSTITUTION,
    annualIncome: formData.annualFamilyIncome ?? 0,
    incomeCertificateNumber: formData.incomeCertificateNo ?? null,
    certificateIssuedBy: formData.incomeCertificateIssuedBy ?? null,
    certificateIssuedDate: formatDateWithDayjs(formData.certificateIssuedDate) ?? null
  };

  if (applicantCareStatus === CARE_STATUS.PARENTS) {
    const fatherUidVault = getUidVaultFromOtpState(formData.fatherAadhaarNumber, otpState);
    const fatherNameAsAadhaar = getAadhaarNameFromOtpState(formData.fatherAadhaarNumber, otpState);

    const motherUidVault = getUidVaultFromOtpState(formData.motherAadhaarNumber, otpState);
    const motherNameAsAadhar = getAadhaarNameFromOtpState(formData.motherAadhaarNumber, otpState);

    payload.fatherName = formData.fatherName ?? null;
    payload.fatherContactNumber = formData.fatherContactNumber ?? null;
    payload.fatherAadhaarVaultNo = fatherUidVault ?? null;
    payload.fatherNameAsAadhaar = fatherNameAsAadhaar ?? null;
    payload.motherName = formData.motherName ?? null;
    payload.motherContactNumber = formData.motherContactNumber ?? null;
    payload.motherAadhaarVaultNo = motherUidVault ?? null;
    payload.motherNameAsAadhar = motherNameAsAadhar ?? null;
  } else if (applicantCareStatus === CARE_STATUS.SINGLE_PARENT) {
    const parentUidVault = getUidVaultFromOtpState(formData.aadhaarNumber, otpState);
    const parentNameAsAadhaar = getAadhaarNameFromOtpState(formData.aadhaarNumber, otpState);

    if (formData.relationshipToApplicant === 'FATHER') {
      payload.fatherName = formData.parentName ?? null;
      payload.fatherContactNumber = formData.contactNumber ?? null;
      payload.fatherAadhaarVaultNo = parentUidVault ?? null;
      payload.fatherNameAsAadhaar = parentNameAsAadhaar ?? null;
    } else {
      payload.motherName = formData.parentName ?? null;
      payload.motherContactNumber = formData.contactNumber ?? null;
      payload.motherAadhaarVaultNo = parentUidVault ?? null;
      payload.motherNameAsAadhar = parentNameAsAadhaar ?? null;
    }
  } else if (applicantCareStatus === CARE_STATUS.ORPHAN) {
    if (currentCareProvider === CURRENT_CARE_PROVIDER.GUARDIAN) {
      const guardianUidVault = getUidVaultFromOtpState(formData.guardianAadhaarNumber, otpState);
      const guardianNameAsAadhaar = getAadhaarNameFromOtpState(
        formData.guardianAadhaarNumber,
        otpState
      );
      payload.guardianName = formData.guardianName ?? null;
      payload.guardianRelation = formData.guardianRelationshipToApplicant ?? null;
      payload.guardianContactNumber = formData.guardianContactNumber ?? null;
      payload.guardianAadhaarVaultNo = guardianUidVault ?? null;
      payload.guardianNameAsAadhaar = guardianNameAsAadhaar ?? null;
    } else if (currentCareProvider === CURRENT_CARE_PROVIDER.INSTITUTION) {
      payload.institutionName = formData.institutionName ?? null;
      payload.institutionRegNumber = formData.institutionRegistrationNumber ?? null;
      payload.institutionContactNumber = formData.institutionContactNumber ?? null;
      payload.institutionAddress = formData.institutionAddress ?? null;
    }
  }

  return payload;
};

const transformAcademicDetails = (formData) => {
  const { applicationId, ...data } = formData;

  return {
    applicationId,
    boardId: data.board ? parseInt(data.board, 10) : null,
    courseName: data.underGradCourseName || null,
    institutionName: data.institutionName || null,
    institutionType: data.institutionType || null,
    institutionLocation: data.institutionLocation || null,
    institutionDistrictId: data.districtOfInstitution || null,
    institutionStateId: data.state ? data.state : null,
    competitiveExamName: data.competitiveExam || null,
    yearOfPassing: data.yearOfCompletion ? parseInt(data.yearOfCompletion, 10) : null,
    registerNumber: data.registerNumber || null,
    marksObtained: data.marksObtained ? parseFloat(data.marksObtained) : null,
    totalMarks: data.totalMarks ? parseFloat(data.totalMarks) : null,
    percentage: data.percentage ? parseFloat(data.percentage) : null,
    cgpaScoreObtained: data.underGradMarksOrScore ? parseFloat(data.underGradMarksOrScore) : 0,
    cgpaTotalScore: data.underGradTotalMarks ? parseFloat(data.underGradTotalMarks) : 0,
    gradeSystem: data.grade === 'Marks' ? 'MARKS' : 'CGPA_OR_OGPA',
    currentInstitutionName: data.currentInstitutionName || null,
    currentInstitutionType: data.currentInstitutionType || null,
    courseMode: data.courseMode ? data.courseMode.toUpperCase() : 'REGULAR',
    academicYear: data.academicYear || null,
    dateOfAdmission: data.dateOfAdmission
      ? new Date(data.dateOfAdmission).toISOString().split('T')[0]
      : null
  };
};

const transformHsAcademicDetails = (formData) => {
  const { applicationId, ...data } = formData;

  return {
    applicationId,
    boardId: data.hsBoard ? parseInt(data.hsBoard, 10) : null,
    courseName: data.underGradCourseName || null,
    institutionName: data.hsInstitutionName || null,
    institutionType: data.hsInstitutionType || null,
    institutionLocation: data.hsInstitutionLocation || null,
    institutionDistrictId: data.hsDistrictOfInstitution || null,
    institutionStateId: data.hsState ? data.hsState.toString() : null,
    competitiveExamName: data.competitiveExam || null,
    yearOfPassing: data.hsYearOfCompletion ? parseInt(data.hsYearOfCompletion, 10) : null,
    registerNumber: data.hsRegisterNumber || null,
    marksObtained: data.hsMarksObtained ? parseFloat(data.hsMarksObtained) : null,
    totalMarks: data.hsTotalMarks ? parseFloat(data.hsTotalMarks) : null,
    percentage: data.hsPercentage ? parseFloat(data.hsPercentage) : null,
    cgpaScoreObtained: data.underGradMarksOrScore ? parseFloat(data.underGradMarksOrScore) : 0,
    cgpaTotalScore: data.underGradTotalMarks ? parseFloat(data.underGradTotalMarks) : 0,
    gradeSystem: data.hsGrade === 'Marks' ? 'MARKS' : 'CGPA_OR_OGPA',
    currentInstitutionName: data.currentInstitutionName || null,
    currentInstitutionType: data.currentInstitutionType || null,
    courseMode: data.courseMode ? data.courseMode.toUpperCase() : 'REGULAR',
    academicYear: data.academicYear || null,
    dateOfAdmission: data.dateOfAdmission
      ? new Date(data.dateOfAdmission).toISOString().split('T')[0]
      : null
  };
};
const transformUgAcademicDetails = (formData) => {
  const { applicationId, ...data } = formData;

  return {
    applicationId,
    boardId: data.university ? parseInt(data.university, 10) : null,
    courseName: data.underGradCourseName || null,
    institutionName: data.instructionName || null,
    institutionType: data.institutionType || null,
    institutionLocation: data.instructionLocation || null,
    institutionDistrictId: data.underGradDistrictOfInstitution || null,
    institutionStateId: data.underGradStateOfInstitution
      ? data.underGradStateOfInstitution.toString()
      : null,
    competitiveExamName: data.competitiveExam || null,
    yearOfPassing: data.underGradYearOfCompletion
      ? parseInt(data.underGradYearOfCompletion, 10)
      : null,
    registerNumber: data.registrationNumber || null,
    marksObtained: data.underGradMarksOrScore
      ? parseFloat(data.underGradMarksOrScore)
      : null,
    totalMarks: data.underGradTotalMarks
      ? parseFloat(data.underGradTotalMarks)
      : null,
    percentage: data.underGradPercentage
      ? parseFloat(data.underGradPercentage)
      : null,
    cgpaScoreObtained: data.underGradMarksOrScore
      ? parseFloat(data.underGradMarksOrScore)
      : 0,
    cgpaTotalScore: data.underGradTotalMarks
      ? parseFloat(data.underGradTotalMarks)
      : 0,
    gradeSystem: data.underGradGradingSystem === 'mark'
      ? 'MARKS'
      : 'CGPA_OR_OGPA',
    currentInstitutionName: data.instructionName || null,
    currentInstitutionType: data.institutionType || null,
    courseMode: 'REGULAR',
    academicYear: null,
    dateOfAdmission: null,
    streamCriteria: data.streamCriteria
  };
};

// utils/formDataBuilder.js

export const buildFormData = (apiData, boardCode, STATE, getCurrentYear) => ({
  board: boardCode,
  institutionName: apiData.institutionName || '',
  institutionType: apiData.institutionType || '',
  institutionLocation: apiData.institutionLocation || '',
  state: STATE.id,
  districtOfInstitution: apiData.institutionDistrictId || '',
  registerNumber: apiData.registerNumber || '',
  yearOfCompletion: getCurrentYear,
  streamCriteria: apiData.streamCriteria,
  grade: apiData.gradeSystem === 'MARKS' ? 'Marks' : 'CGPA',
  marksObtained: apiData.marksObtained?.toString() || '',
  totalMarks: apiData.totalMarks?.toString() || '',
  percentage: apiData.percentage?.toString() || '',
  hsBoard: boardCode,
  hsInstitutionType: apiData.institutionType || '',
  hsInstitutionName: apiData.institutionName || '',
  hsInstitutionLocation: apiData.institutionLocation || '',
  hsState: STATE.id,
  hsDistrictOfInstitution: apiData.institutionDistrictId || '',
  hsRegisterNumber: apiData.registerNumber || '',
  hsYearOfCompletion: getCurrentYear,
  hsGrade: apiData.gradeSystem === 'MARKS' ? 'Marks' : 'CGPA',
  hsMarksObtained: apiData.marksObtained?.toString() || '',
  hsTotalMarks: apiData.totalMarks?.toString() || '',
  hsPercentage: apiData.percentage?.toString() || '',
  currentInstitutionType: apiData.currentInstitutionType || '',
  currentInstitutionName: apiData.currentInstitutionName || '',
  courseMode: apiData.courseMode || '',
  academicYear: apiData.academicYear || '',
  dateOfAdmission: apiData.dateOfAdmission || '',
  competitiveExam: apiData.competitiveExamName || '',
  underGradCourseName: apiData.courseName || '',
  underGradGradingSystem: apiData.gradeSystem === 'MARKS' ? 'mark' : 'cgpa',
  underGradMarksOrScore: apiData.cgpaScoreObtained?.toString() || '',
  underGradTotalMarks: apiData.cgpaTotalScore?.toString() || '',
  underGradPercentage: apiData.percentage?.toString() || '',
  underGradYearOfCompletion: getCurrentYear
});

export {
  transformFormData, transformParentDetailsToPayload,
  transformBankDetails, transformAcademicDetails,
  transformHsAcademicDetails, transformUgAcademicDetails
};
